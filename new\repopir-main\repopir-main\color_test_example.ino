/*
 * Color Test Example for Staircase Automation
 * 
 * This simplified version demonstrates the multi-color features
 * without the full motion detection system. Use this to test
 * colors and animations before implementing the full system.
 */

#include <FastLED.h>

// LED Configuration
#define LED_DATA_PIN 9
#define NUM_LEDS 166
#define LED_TYPE WS2812B
#define COLOR_ORDER GRB
#define BRIGHTNESS 150

// Animation settings
#define FADE_STEPS 20
#define ANIMATION_SPEED 50

// LED array
CRGB leds[NUM_LEDS];

// Color cycling variables
int currentColorIndex = 0;
bool useRandomColors = true;
bool useRainbowMode = false;
int rainbowHue = 0;

// Color palette - same as main project
CRGB colorPalette[] = {
  CRGB::White,
  CRGB::DeepSkyBlue,
  CRGB::SpringGreen,
  CRGB::Gold,
  CRGB::Magenta,
  CRGB::Orange,
  CRGB::Purple,
  CRGB::Cyan,
  CRGB::LimeGreen,
  CRGB::HotPink,
  CRGB::Yellow,
  CRGB::Turquoise
};

#define NUM_COLORS (sizeof(colorPalette) / sizeof(colorPalette[0]))

// Current animation color
CRGB currentAnimationColor = CRGB::White;

void setup() {
  Serial.begin(9600);
  Serial.println("Color Test Example Starting...");
  
  // Initialize LED strip
  FastLED.addLeds<LED_TYPE, LED_DATA_PIN, COLOR_ORDER>(leds, NUM_LEDS);
  FastLED.setBrightness(BRIGHTNESS);
  FastLED.clear();
  FastLED.show();
  
  // Initialize random seed
  randomSeed(analogRead(A0));
  
  Serial.println("Commands:");
  Serial.println("1 - Test upward animation");
  Serial.println("2 - Test downward animation");
  Serial.println("3 - Toggle random colors");
  Serial.println("4 - Toggle rainbow mode");
  Serial.println("5 - Show all colors");
  Serial.println("6 - Clear all LEDs");
  Serial.println();
}

void loop() {
  // Check for serial commands
  if (Serial.available()) {
    int command = Serial.parseInt();
    
    switch (command) {
      case 1:
        Serial.println("Testing upward animation...");
        testUpwardAnimation();
        break;
        
      case 2:
        Serial.println("Testing downward animation...");
        testDownwardAnimation();
        break;
        
      case 3:
        useRandomColors = !useRandomColors;
        Serial.print("Random colors: ");
        Serial.println(useRandomColors ? "ON" : "OFF");
        break;
        
      case 4:
        useRainbowMode = !useRainbowMode;
        Serial.print("Rainbow mode: ");
        Serial.println(useRainbowMode ? "ON" : "OFF");
        break;
        
      case 5:
        Serial.println("Showing all colors...");
        showAllColors();
        break;
        
      case 6:
        Serial.println("Clearing LEDs...");
        FastLED.clear();
        FastLED.show();
        break;
        
      default:
        if (command != 0) {
          Serial.println("Invalid command. Use 1-6.");
        }
        break;
    }
  }
  
  delay(100);
}

void selectAnimationColor() {
  if (useRainbowMode) {
    // Rainbow mode
    currentAnimationColor = CHSV(rainbowHue, 255, 255);
    rainbowHue += 30;
    if (rainbowHue >= 255) rainbowHue = 0;
  } else {
    // Regular color palette
    if (useRandomColors) {
      currentAnimationColor = colorPalette[random(NUM_COLORS)];
    } else {
      currentAnimationColor = colorPalette[currentColorIndex % NUM_COLORS];
      currentColorIndex++;
    }
  }
  
  Serial.print("Selected color: ");
  printColorName(currentAnimationColor);
}

void testUpwardAnimation() {
  selectAnimationColor();
  
  int ledsPerStep = NUM_LEDS / FADE_STEPS;
  
  // Animate from bottom to top
  for (int step = 0; step < FADE_STEPS; step++) {
    int endLed = min((step + 1) * ledsPerStep, NUM_LEDS);
    
    // Light up LEDs for this step
    for (int i = step * ledsPerStep; i < endLed; i++) {
      if (useRainbowMode) {
        int hue = (rainbowHue + (i * 255 / NUM_LEDS)) % 255;
        leds[i] = CHSV(hue, 255, 255);
      } else {
        leds[i] = currentAnimationColor;
      }
    }
    
    // Add trailing fade
    if (step > 0) {
      int fadeStart = max(0, (step - 2) * ledsPerStep);
      int fadeEnd = step * ledsPerStep;
      for (int i = fadeStart; i < fadeEnd; i++) {
        leds[i].fadeToBlackBy(64);
      }
    }
    
    FastLED.show();
    delay(ANIMATION_SPEED);
  }
  
  // Fill entire strip
  if (useRainbowMode) {
    for (int i = 0; i < NUM_LEDS; i++) {
      int hue = (rainbowHue + (i * 255 / NUM_LEDS)) % 255;
      leds[i] = CHSV(hue, 255, 255);
    }
  } else {
    fill_solid(leds, NUM_LEDS, currentAnimationColor);
  }
  FastLED.show();
  
  Serial.println("Animation complete. Send command 6 to clear.");
}

void testDownwardAnimation() {
  selectAnimationColor();
  
  int ledsPerStep = NUM_LEDS / FADE_STEPS;
  
  // Animate from top to bottom
  for (int step = 0; step < FADE_STEPS; step++) {
    int startLed = max(NUM_LEDS - (step + 1) * ledsPerStep, 0);
    
    // Light up LEDs for this step
    for (int i = NUM_LEDS - step * ledsPerStep - 1; i >= startLed; i--) {
      if (useRainbowMode) {
        int hue = (rainbowHue + (i * 255 / NUM_LEDS)) % 255;
        leds[i] = CHSV(hue, 255, 255);
      } else {
        leds[i] = currentAnimationColor;
      }
    }
    
    // Add trailing fade
    if (step > 0) {
      int fadeStart = NUM_LEDS - step * ledsPerStep;
      int fadeEnd = min(NUM_LEDS, NUM_LEDS - (step - 2) * ledsPerStep);
      for (int i = fadeStart; i < fadeEnd; i++) {
        leds[i].fadeToBlackBy(64);
      }
    }
    
    FastLED.show();
    delay(ANIMATION_SPEED);
  }
  
  // Fill entire strip
  if (useRainbowMode) {
    for (int i = 0; i < NUM_LEDS; i++) {
      int hue = (rainbowHue + (i * 255 / NUM_LEDS)) % 255;
      leds[i] = CHSV(hue, 255, 255);
    }
  } else {
    fill_solid(leds, NUM_LEDS, currentAnimationColor);
  }
  FastLED.show();
  
  Serial.println("Animation complete. Send command 6 to clear.");
}

void showAllColors() {
  for (int c = 0; c < NUM_COLORS; c++) {
    Serial.print("Color ");
    Serial.print(c + 1);
    Serial.print(": ");
    printColorName(colorPalette[c]);
    
    fill_solid(leds, NUM_LEDS, colorPalette[c]);
    FastLED.show();
    delay(1000);
  }
  
  FastLED.clear();
  FastLED.show();
  Serial.println("Color showcase complete.");
}

void printColorName(CRGB color) {
  if (color == CRGB::White) Serial.println("White");
  else if (color == CRGB::DeepSkyBlue) Serial.println("Deep Sky Blue");
  else if (color == CRGB::SpringGreen) Serial.println("Spring Green");
  else if (color == CRGB::Gold) Serial.println("Gold");
  else if (color == CRGB::Magenta) Serial.println("Magenta");
  else if (color == CRGB::Orange) Serial.println("Orange");
  else if (color == CRGB::Purple) Serial.println("Purple");
  else if (color == CRGB::Cyan) Serial.println("Cyan");
  else if (color == CRGB::LimeGreen) Serial.println("Lime Green");
  else if (color == CRGB::HotPink) Serial.println("Hot Pink");
  else if (color == CRGB::Yellow) Serial.println("Yellow");
  else if (color == CRGB::Turquoise) Serial.println("Turquoise");
  else {
    Serial.print("RGB(");
    Serial.print(color.r);
    Serial.print(",");
    Serial.print(color.g);
    Serial.print(",");
    Serial.print(color.b);
    Serial.println(")");
  }
}
