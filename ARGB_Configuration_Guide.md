# ARGB LED Strip Configuration Guide

## ARGB Strip Types Supported:
- **WS2812B** (Most common, 5V, 60 LEDs/meter)
- **WS2815** (5V, backup data line, more reliable)
- **SK6812** (5V, similar to WS2812B)
- **WS2813** (5V, backup data line)

## Code Configuration:

### 1. LED Strip Type Configuration:
```cpp
#define LED_TYPE WS2812B    // Change based on your strip
#define COLOR_ORDER GRB     // Most strips use GRB, some use RGB
```

### 2. Staircase Configuration:
```cpp
#define NUM_STEPS 8         // Change to match your stairs
#define LEDS_PER_STEP 15    // Adjust based on strip density
```

### 3. Timing Configuration:
```cpp
#define STEP_DELAY 250      // Time between lighting steps (ms)
#define TIMEOUT_DURATION 30000  // Auto-off timeout (ms)
#define FADE_SPEED 20       // Fade effect speed
```

### 4. Power and Brightness:
```cpp
#define BRIGHTNESS 120      // 0-255 (lower = less power)
#define POWER_LIMIT_MW 4000 // Power limit in milliwatts
```

## ARGB Strip Installation Tips:

### 1. Strip Placement:
- Install under each step for indirect lighting
- Use aluminum channels for heat dissipation and diffusion
- Ensure strips are protected from moisture
- Leave 10cm extra wire at each end for connections

### 2. Data Signal:
- Keep data wire as short as possible
- Use twisted pair for data and ground if running long distances
- Add 330Ω resistor between Arduino pin and strip data input
- For very long runs, consider level shifters (3.3V to 5V)

### 3. Power Distribution:
- **Short strips (<3m)**: Power from one end
- **Long strips (>3m)**: Inject power every 2-3 meters
- Use thick wires for power (14-16 AWG for high current)
- Add fuses for safety (10A fast-blow recommended)

### 4. Grounding:
- Connect all grounds together (Arduino, power supply, LED strip)
- Use star grounding configuration for best results
- Keep ground wires short and thick

## Troubleshooting ARGB Issues:

### 1. LEDs Not Lighting:
- Check power supply voltage (should be 5V ±0.25V)
- Verify data wire connections
- Test with simple FastLED example code
- Check if strip is damaged (test individual sections)

### 2. Wrong Colors:
- Try changing COLOR_ORDER from GRB to RGB or BGR
- Check if strip type matches LED_TYPE definition
- Verify power supply can handle the current load

### 3. Flickering or Glitches:
- Add 1000µF capacitor near power input
- Check for loose connections
- Reduce brightness to lower power consumption
- Add 330Ω resistor on data line if not present

### 4. Only First Few LEDs Work:
- Power supply insufficient - upgrade to higher amperage
- Data signal degraded - add signal repeater/level shifter
- Strip damaged at specific point - test sections individually

## Power Calculation Examples:

### Example 1: 8 Steps, 15 LEDs per step
- Total LEDs: 8 × 15 = 120 LEDs
- Max current: 120 × 60mA = 7.2A
- Recommended PSU: 5V 10A

### Example 2: 12 Steps, 20 LEDs per step  
- Total LEDs: 12 × 20 = 240 LEDs
- Max current: 240 × 60mA = 14.4A
- Recommended PSU: 5V 20A (or multiple smaller PSUs)

## Color Customization:

### Predefined Colors in Code:
```cpp
CRGB iceBlue = CRGB(100, 150, 255);    // Current ice blue
CRGB warmWhite = CRGB(255, 220, 180);  // Warm white option
CRGB coolWhite = CRGB(200, 220, 255);  // Cool white option
```

### Custom Colors:
```cpp
CRGB myColor = CRGB(R, G, B);  // R, G, B values 0-255
// Examples:
CRGB red = CRGB(255, 0, 0);
CRGB green = CRGB(0, 255, 0);
CRGB purple = CRGB(128, 0, 128);
```

## Safety Considerations:

1. **Electrical Safety:**
   - Use proper fuses and circuit breakers
   - Ensure all connections are secure and insulated
   - Use appropriate wire gauges for current loads
   - Install GFCI protection for wet locations

2. **Fire Safety:**
   - Don't exceed strip power ratings
   - Use aluminum channels for heat dissipation
   - Avoid covering strips with flammable materials
   - Regular inspection of connections

3. **Code Safety Features:**
   - Power limiting prevents overcurrent
   - Timeout function prevents lights staying on indefinitely
   - Sensor debouncing prevents false triggers

## Performance Optimization:

1. **Reduce Power Consumption:**
   - Lower brightness setting
   - Use fewer LEDs per step
   - Implement dimming during late hours

2. **Improve Responsiveness:**
   - Reduce STEP_DELAY for faster sequences
   - Optimize sensor placement for better detection
   - Use faster microcontroller if needed

3. **Enhance Reliability:**
   - Add backup power (UPS)
   - Use redundant data lines (WS2815/WS2813)
   - Implement watchdog timer in code
