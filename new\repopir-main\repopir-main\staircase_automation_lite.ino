/*
 * Staircase Automation System - Memory Optimized Version
 * Features: Direction-aware lighting with multiple colors
 * Optimized for Arduino Nano (limited memory)
 */

#include <FastLED.h>

// Pin definitions
#define PIR_BOTTOM_PIN 2
#define PIR_TOP_PIN 3
#define LED_DATA_PIN 9
#define LED_POWER_PIN 7

// LED strip configuration
#define NUM_LEDS 166
#define LED_TYPE WS2812B
#define COLOR_ORDER GRB
#define BRIGHTNESS 150

// Timing constants
#define MOTION_TIMEOUT 30000
#define DEBOUNCE_TIME 500
#define ANIMATION_SPEED 50
#define FADE_STEPS 20

// LED array
CRGB leds[NUM_LEDS];

// State variables
bool motionDetected = false;
bool ledsOn = false;
unsigned long lastMotionTime = 0;
unsigned long lastPirBottomTime = 0;
unsigned long lastPirTopTime = 0;
bool pirBottomState = false;
bool pirTopState = false;

// Direction detection
unsigned long lastBottomTrigger = 0;
unsigned long lastTopTrigger = 0;
bool directionUp = true;
unsigned long directionWindow = 3000;

// Animation variables
int currentStep = 0;
bool animatingUp = false;
bool animatingDown = false;
bool animationComplete = false;
unsigned long lastAnimationTime = 0;

// Color system - optimized for memory
#define NUM_COLORS 6
int currentColorIndex = 0;
bool useRandomColors = true;

// Color palette stored in flash memory
const uint32_t colors[NUM_COLORS] PROGMEM = {
  0xFFFFFF,  // White
  0x00BFFF,  // Blue
  0x00FF7F,  // Green
  0xFFD700,  // Gold
  0xFF00FF,  // Magenta
  0xFF4500   // Orange
};

CRGB currentColor = CRGB::White;

void setup() {
  Serial.begin(9600);
  Serial.println("Staircase Automation Lite");
  
  pinMode(PIR_BOTTOM_PIN, INPUT);
  pinMode(PIR_TOP_PIN, INPUT);
  pinMode(LED_POWER_PIN, OUTPUT);
  
  FastLED.addLeds<LED_TYPE, LED_DATA_PIN, COLOR_ORDER>(leds, NUM_LEDS);
  FastLED.setBrightness(BRIGHTNESS);
  FastLED.clear();
  FastLED.show();
  
  digitalWrite(LED_POWER_PIN, HIGH);
  randomSeed(analogRead(A0));
  
  startupAnimation();
  Serial.println("Ready!");
}

void loop() {
  readPirSensors();
  handleMotionLogic();
  handleAnimations();
  handleTimeout();
  delay(10);
}

void readPirSensors() {
  unsigned long currentTime = millis();

  // Bottom PIR
  bool bottomReading = digitalRead(PIR_BOTTOM_PIN);
  if (bottomReading != pirBottomState && 
      (currentTime - lastPirBottomTime) > DEBOUNCE_TIME) {
    pirBottomState = bottomReading;
    lastPirBottomTime = currentTime;
    
    if (bottomReading) {
      Serial.println("Bottom");
      lastBottomTrigger = currentTime;
      detectDirection();
    }
  }

  // Top PIR
  bool topReading = digitalRead(PIR_TOP_PIN);
  if (topReading != pirTopState && 
      (currentTime - lastPirTopTime) > DEBOUNCE_TIME) {
    pirTopState = topReading;
    lastPirTopTime = currentTime;
    
    if (topReading) {
      Serial.println("Top");
      lastTopTrigger = currentTime;
      detectDirection();
    }
  }
}

void handleMotionLogic() {
  if (pirBottomState || pirTopState) {
    motionDetected = true;
    lastMotionTime = millis();
  }
}

void detectDirection() {
  unsigned long currentTime = millis();
  
  if (lastBottomTrigger > 0 && lastTopTrigger > 0) {
    unsigned long timeDiff = abs((long)(lastTopTrigger - lastBottomTrigger));
    
    if (timeDiff <= directionWindow) {
      directionUp = (lastBottomTrigger < lastTopTrigger);
      
      Serial.print("Direction: ");
      Serial.println(directionUp ? "UP" : "DOWN");
      
      triggerAnimation(directionUp);
      resetTriggers();
    }
  }
  
  // Single sensor timeout
  if (lastBottomTrigger > 0 && (currentTime - lastBottomTrigger) > directionWindow) {
    if (lastTopTrigger == 0) {
      directionUp = true;
      triggerAnimation(true);
    }
    resetTriggers();
  }
  
  if (lastTopTrigger > 0 && (currentTime - lastTopTrigger) > directionWindow) {
    if (lastBottomTrigger == 0) {
      directionUp = false;
      triggerAnimation(false);
    }
    resetTriggers();
  }
}

void resetTriggers() {
  lastBottomTrigger = 0;
  lastTopTrigger = 0;
}

void triggerAnimation(bool upward) {
  if (!ledsOn || animationComplete) {
    selectColor();
    
    ledsOn = true;
    currentStep = 0;
    animatingUp = upward;
    animatingDown = !upward;
    animationComplete = false;
    lastAnimationTime = millis();
    
    Serial.print(upward ? "Up: " : "Down: ");
    printColor();
  } else {
    lastMotionTime = millis();
  }
}

void selectColor() {
  int index;
  if (useRandomColors) {
    index = random(NUM_COLORS);
  } else {
    index = currentColorIndex % NUM_COLORS;
    currentColorIndex++;
  }
  
  uint32_t colorValue = pgm_read_dword(&colors[index]);
  currentColor = CRGB(colorValue);
}

void printColor() {
  Serial.print("RGB(");
  Serial.print(currentColor.r);
  Serial.print(",");
  Serial.print(currentColor.g);
  Serial.print(",");
  Serial.print(currentColor.b);
  Serial.println(")");
}

void handleAnimations() {
  unsigned long currentTime = millis();
  
  if ((animatingUp || animatingDown) && 
      (currentTime - lastAnimationTime) > ANIMATION_SPEED) {
    
    if (animatingUp) {
      animateUpward();
    } else if (animatingDown) {
      animateDownward();
    }
    
    lastAnimationTime = currentTime;
  }
}

void animateUpward() {
  int ledsPerStep = NUM_LEDS / FADE_STEPS;
  int endLed = min((currentStep + 1) * ledsPerStep, NUM_LEDS);

  for (int i = currentStep * ledsPerStep; i < endLed; i++) {
    leds[i] = currentColor;
  }

  if (currentStep > 0) {
    int fadeStart = max(0, (currentStep - 2) * ledsPerStep);
    int fadeEnd = currentStep * ledsPerStep;
    for (int i = fadeStart; i < fadeEnd; i++) {
      leds[i].fadeToBlackBy(64);
    }
  }

  FastLED.show();
  currentStep++;

  if (currentStep >= FADE_STEPS) {
    animatingUp = false;
    animationComplete = true;
    fill_solid(leds, NUM_LEDS, currentColor);
    FastLED.show();
  }
}

void animateDownward() {
  int ledsPerStep = NUM_LEDS / FADE_STEPS;
  int startLed = max(NUM_LEDS - (currentStep + 1) * ledsPerStep, 0);

  for (int i = NUM_LEDS - currentStep * ledsPerStep - 1; i >= startLed; i--) {
    leds[i] = currentColor;
  }

  if (currentStep > 0) {
    int fadeStart = NUM_LEDS - currentStep * ledsPerStep;
    int fadeEnd = min(NUM_LEDS, NUM_LEDS - (currentStep - 2) * ledsPerStep);
    for (int i = fadeStart; i < fadeEnd; i++) {
      leds[i].fadeToBlackBy(64);
    }
  }

  FastLED.show();
  currentStep++;

  if (currentStep >= FADE_STEPS) {
    animatingDown = false;
    animationComplete = true;
    fill_solid(leds, NUM_LEDS, currentColor);
    FastLED.show();
  }
}

void handleTimeout() {
  if (motionDetected && ledsOn && 
      (millis() - lastMotionTime) > MOTION_TIMEOUT) {
    
    if (!pirBottomState && !pirTopState) {
      fadeOutLeds();
      resetSystem();
    }
  }
}

void fadeOutLeds() {
  for (int brightness = BRIGHTNESS; brightness >= 0; brightness -= 5) {
    FastLED.setBrightness(brightness);
    FastLED.show();
    delay(50);
  }
  
  FastLED.clear();
  FastLED.show();
  FastLED.setBrightness(BRIGHTNESS);
}

void resetSystem() {
  ledsOn = false;
  motionDetected = false;
  currentStep = 0;
  animatingUp = false;
  animatingDown = false;
  animationComplete = false;
  resetTriggers();
}

void startupAnimation() {
  for (int i = 0; i < 3; i++) {
    fill_solid(leds, NUM_LEDS, CRGB::Blue);
    FastLED.show();
    delay(200);
    FastLED.clear();
    FastLED.show();
    delay(200);
  }
}
