# Direction-Aware Staircase Automation

## Overview

The updated staircase automation system now includes intelligent direction detection that determines the movement direction based on the sequence of PIR sensor triggers. The LED animation follows the actual direction of movement for a more natural and intuitive lighting experience.

## How Direction Detection Works

### Basic Principle
The system monitors which PIR sensor is triggered first and uses timing analysis to determine movement direction:

- **Upward Movement**: Bottom sensor triggers first, then top sensor
- **Downward Movement**: Top sensor triggers first, then bottom sensor

### Detection Algorithm

```cpp
// Key parameters
#define directionWindow 3000  // 3 seconds to detect direction sequence

// Detection logic:
1. Record timestamp when each sensor triggers
2. Compare timestamps within the direction window
3. Determine direction based on sequence
4. Trigger appropriate animation
```

## Direction Detection Modes

### 1. Dual Sensor Detection (Primary Mode)
**Most Accurate Method**

```
Scenario: Person walks up stairs
Timeline:
  T+0ms:   Bottom PIR triggers → Record timestamp
  T+1500ms: Top PIR triggers → Record timestamp
  Result:   Bottom first → UPWARD direction detected
  Action:   Start bottom-to-top LED animation
```

### 2. Single Sensor Fallback
**Backup Method for Edge Cases**

```
Scenario: Only one sensor triggers (person stops mid-stair)
Timeline:
  T+0ms:    Bottom PIR triggers → Record timestamp
  T+3000ms: Direction window expires, no top trigger
  Result:   Bottom only → Assume UPWARD movement
  Action:   Start bottom-to-top LED animation
```

### 3. Timeout Handling
**Prevents False Triggers**

```
Scenario: Sensor triggers but no movement follows
Timeline:
  T+0ms:    Bottom PIR triggers → Record timestamp
  T+3000ms: Direction window expires
  T+3001ms: Clear trigger timestamp
  Result:   No direction determined, wait for next trigger
```

## Animation Enhancements

### Directional Wave Effect
The LED animation now includes a smooth wave effect that follows the detected direction:

#### Upward Animation (Bottom → Top)
```cpp
// Progressive lighting from index 0 to NUM_LEDS-1
for (int i = currentStep * ledsPerStep; i < endLed; i++) {
  leds[i] = CRGB::White;
}

// Trailing fade effect for smooth wave
if (currentStep > 0) {
  int fadeStart = max(0, (currentStep - 2) * ledsPerStep);
  int fadeEnd = currentStep * ledsPerStep;
  for (int i = fadeStart; i < fadeEnd; i++) {
    leds[i].fadeToBlackBy(64);
  }
}
```

#### Downward Animation (Top → Bottom)
```cpp
// Progressive lighting from index NUM_LEDS-1 to 0
for (int i = NUM_LEDS - currentStep * ledsPerStep - 1; i >= startLed; i--) {
  leds[i] = CRGB::White;
}

// Trailing fade effect for smooth wave
if (currentStep > 0) {
  int fadeStart = NUM_LEDS - currentStep * ledsPerStep;
  int fadeEnd = min(NUM_LEDS, NUM_LEDS - (currentStep - 2) * ledsPerStep);
  for (int i = fadeStart; i < fadeEnd; i++) {
    leds[i].fadeToBlackBy(64);
  }
}
```

## Configuration Parameters

### Timing Settings
```cpp
// Direction detection window (milliseconds)
#define directionWindow 3000    // Time to wait for second sensor

// PIR sensor debouncing (milliseconds)
#define DEBOUNCE_TIME 500       // Prevent false triggers

// Animation timing (milliseconds)
#define ANIMATION_SPEED 50      // Speed of wave progression
#define FADE_STEPS 20           // Number of animation steps
```

### Sensitivity Tuning
```cpp
// Adjust these values based on your staircase:

// For faster walkers (reduce window)
#define directionWindow 2000    // 2 seconds

// For slower walkers (increase window)
#define directionWindow 4000    // 4 seconds

// For very sensitive PIRs (increase debounce)
#define DEBOUNCE_TIME 1000      // 1 second debounce
```

## Serial Monitor Output

### Normal Operation
```
System Ready!
Motion detected at BOTTOM
Motion detected at TOP
Direction detected: UPWARD (Bottom → Top)
Starting upward animation
Upward animation complete
Timeout reached, turning off LEDs
```

### Single Sensor Trigger
```
Motion detected at BOTTOM
Single sensor trigger: UPWARD (Bottom only)
Starting upward animation
Upward animation complete
```

### Direction Change
```
Motion detected at TOP
Motion detected at BOTTOM
Direction detected: DOWNWARD (Top → Bottom)
Starting downward animation
Downward animation complete
```

## Troubleshooting Direction Detection

### Problem: Wrong Direction Detected

**Symptoms:**
- Animation goes opposite to actual movement
- Inconsistent direction detection

**Possible Causes:**
1. **PIR sensor placement**: Sensors too close together
2. **Timing issues**: Direction window too short/long
3. **Sensor sensitivity**: One sensor more sensitive than other

**Solutions:**
```cpp
// Increase direction window for slower movement
#define directionWindow 4000

// Adjust PIR sensitivity potentiometers to match
// Test with different walking speeds

// Add debug output to monitor timing
Serial.print("Bottom trigger: ");
Serial.print(lastBottomTrigger);
Serial.print(", Top trigger: ");
Serial.println(lastTopTrigger);
```

### Problem: No Direction Detected

**Symptoms:**
- Sensors trigger but no animation starts
- "Single sensor trigger" messages only

**Possible Causes:**
1. **One sensor not working**: Check power and connections
2. **Direction window too short**: Person moves too slowly
3. **Sensor placement**: Dead zones between sensors

**Solutions:**
```cpp
// Extend direction window
#define directionWindow 5000

// Add sensor status monitoring
void debugSensors() {
  Serial.print("PIR Bottom: ");
  Serial.print(digitalRead(PIR_BOTTOM_PIN));
  Serial.print(", PIR Top: ");
  Serial.println(digitalRead(PIR_TOP_PIN));
}
```

### Problem: False Direction Changes

**Symptoms:**
- Animation changes direction unexpectedly
- Multiple triggers from same person

**Possible Causes:**
1. **PIR sensor delay settings**: Sensors retrigger too quickly
2. **Debounce time too short**: Multiple false triggers
3. **Environmental interference**: Heat sources, air movement

**Solutions:**
```cpp
// Increase debounce time
#define DEBOUNCE_TIME 1000

// Adjust PIR delay potentiometers to minimum
// Shield sensors from environmental interference
```

## Advanced Features

### Bidirectional Movement Detection
The system can handle people moving in both directions simultaneously:

```cpp
// Future enhancement: Track multiple people
struct Person {
  unsigned long bottomTime;
  unsigned long topTime;
  bool directionUp;
  bool active;
};

Person people[2];  // Track up to 2 people
```

### Adaptive Timing
Automatically adjust direction window based on detected movement patterns:

```cpp
// Learn from successful detections
void adaptTiming() {
  if (lastBottomTrigger > 0 && lastTopTrigger > 0) {
    unsigned long actualTime = abs(lastTopTrigger - lastBottomTrigger);
    directionWindow = actualTime * 1.5;  // Add 50% margin
  }
}
```

### Smart Timeout
Different timeout values based on direction and usage patterns:

```cpp
// Longer timeout for upward movement (going to bed)
unsigned long getTimeoutValue() {
  if (directionUp) {
    return MOTION_TIMEOUT * 2;  // Double timeout going up
  }
  return MOTION_TIMEOUT;
}
```

## Installation Considerations

### PIR Sensor Placement
For optimal direction detection:

1. **Height**: Mount both sensors at same height (1-2 meters)
2. **Angle**: Slight downward angle to detect foot traffic
3. **Distance**: Minimum 2 meters apart for clear direction detection
4. **Coverage**: Ensure no dead zones between sensors

### Testing Procedure
1. **Walk slowly upward**: Verify upward animation
2. **Walk slowly downward**: Verify downward animation
3. **Walk quickly both directions**: Test timing limits
4. **Stop mid-stair**: Test single sensor fallback
5. **Multiple people**: Test concurrent usage

### Performance Optimization
```cpp
// For very long staircases (>10 meters)
#define directionWindow 6000    // Longer detection window

// For short staircases (<3 meters)
#define directionWindow 1500    // Shorter detection window

// For high-traffic areas
#define DEBOUNCE_TIME 200       // Faster response
```

This direction-aware system provides a much more intuitive and natural lighting experience that follows the actual movement of people on the staircase.
