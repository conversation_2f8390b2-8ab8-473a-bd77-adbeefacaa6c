# Troubleshooting Guide - Staircase Automation

## Quick Diagnostic Checklist

### Power Issues
- [ ] 12V power supply LED indicator on
- [ ] Voltage at LED strip: 11.5-12.5V
- [ ] Buck converter output: 4.8-5.2V
- [ ] Arduino power LED on
- [ ] PIR sensor power LEDs on

### Connection Issues
- [ ] All ground connections secure
- [ ] LED data line connected properly
- [ ] PIR signal wires not reversed
- [ ] No loose connections in terminal blocks

### Software Issues
- [ ] Arduino IDE can communicate with board
- [ ] FastLED library installed correctly
- [ ] Correct board and processor selected
- [ ] Serial monitor shows startup messages

## Common Problems and Solutions

### Problem: LEDs Don't Light Up

#### Symptoms
- Arduino starts normally
- Serial monitor shows motion detection
- No LED response

#### Possible Causes and Solutions

1. **Power Supply Issues**
   ```
   Check: Measure voltage at LED strip
   Expected: 11.5-12.5V DC
   Solution: Verify power supply capacity and connections
   ```

2. **Data Line Problems**
   ```
   Check: LED data pin connection (D6)
   Expected: Clean digital signal
   Solution: Check level shifter, add 470Ω resistor
   ```

3. **LED Strip Defects**
   ```
   Check: Test with simple FastLED example
   Expected: Basic colors work
   Solution: Replace defective strip section
   ```

### Problem: PIR Sensors Not Responding

#### Symptoms
- No "Motion detected" messages in serial monitor
- LEDs never activate
- System appears to run normally otherwise

#### Possible Causes and Solutions

1. **Power Issues**
   ```
   Check: 5V at PIR sensor VCC pins
   Expected: 4.8-5.2V DC
   Solution: Verify buck converter output, check connections
   ```

2. **Signal Line Problems**
   ```
   Check: Continuity on 7m signal cable
   Expected: <1Ω resistance end-to-end
   Solution: Repair cable, add pull-up resistor (10kΩ)
   ```

3. **Sensor Configuration**
   ```
   Check: PIR sensitivity and delay potentiometers
   Expected: Proper adjustment for environment
   Solution: Adjust pots, test with LED indicator
   ```

### Problem: False Triggers

#### Symptoms
- LEDs activate without motion
- Frequent on/off cycling
- System responds to environmental changes
- "Trigger rejected" messages in serial monitor

#### Enhanced False Detection Prevention

The system now includes multiple layers of protection:

1. **Automatic Trigger Validation**
   ```
   Check: Serial monitor for rejection messages
   Expected: "Motion detected at BOTTOM (validated)"
   Rejection: "Trigger rejected: Too soon after last trigger"
   Solution: Adjust minTriggerInterval in code
   ```

2. **Oscillation Detection**
   ```
   Check: "Rapid oscillation detected" messages
   Expected: Stable sensor behavior
   Solution: Check sensor mounting, reduce sensitivity
   ```

3. **Direction Confirmation System**
   ```
   Check: "Direction needs more confirmation" messages
   Expected: "Direction confirmed: UPWARD/DOWNWARD"
   Solution: Wait for multiple confirmations or adjust requiredConfirmations
   ```

#### Traditional Solutions

1. **PIR Sensitivity Too High**
   ```
   Check: Sensitivity potentiometer setting
   Expected: Minimum setting that still detects motion
   Solution: Reduce sensitivity gradually
   ```

2. **Environmental Interference**
   ```
   Check: Heat sources, air currents, sunlight
   Expected: Stable thermal environment
   Solution: Relocate sensors, add shields
   ```

3. **Electrical Noise**
   ```
   Check: EMI from other devices
   Expected: Clean power and signal lines
   Solution: Add ferrite cores, improve grounding
   ```

### Problem: Intermittent Operation

#### Symptoms
- System works sometimes but not consistently
- Random resets or lockups
- Erratic LED behavior

#### Possible Causes and Solutions

1. **Power Supply Instability**
   ```
   Check: Voltage under load, ripple
   Expected: Stable voltage, <100mV ripple
   Solution: Add filter capacitors, upgrade power supply
   ```

2. **Loose Connections**
   ```
   Check: All screw terminals, solder joints
   Expected: Secure mechanical connections
   Solution: Retighten, resolder as needed
   ```

3. **Software Issues**
   ```
   Check: Serial monitor for error messages
   Expected: Clean operation logs
   Solution: Add watchdog timer, improve error handling
   ```

## Advanced Diagnostics

### Using Serial Monitor for Debugging

#### Enable Detailed Logging
Add this to your code for more verbose output:

```cpp
// Add at top of file
#define DEBUG_MODE 1

// Add in loop() function
#if DEBUG_MODE
  Serial.print("PIR Bottom: ");
  Serial.print(pirBottomState);
  Serial.print(", PIR Top: ");
  Serial.print(pirTopState);
  Serial.print(", LEDs On: ");
  Serial.println(ledsOn);
#endif
```

#### Interpreting Debug Output
```
Normal operation:
PIR Bottom: 0, PIR Top: 0, LEDs On: 0
Motion detected at BOTTOM
PIR Bottom: 1, PIR Top: 0, LEDs On: 1
Starting upward animation

Problem indicators:
- PIR values stuck at 1: Sensor wiring issue
- No motion messages: Signal line problem
- LEDs On but no light: Power or data issue
```

### Voltage Testing Points

#### Critical Measurements
```
Test Point          | Expected Voltage | Tolerance
--------------------|------------------|----------
12V Supply Output   | 12.0V           | ±0.5V
Buck Converter Out  | 5.0V            | ±0.2V
Arduino 5V Pin      | 5.0V            | ±0.2V
PIR VCC (both)      | 5.0V            | ±0.2V
LED Strip Power     | 12.0V           | ±0.5V
```

#### Signal Testing
```
Signal              | Expected Level   | Notes
--------------------|------------------|----------
PIR Output (idle)   | 0V              | Should be stable
PIR Output (active) | 3.3-5V          | Clean transition
LED Data Line       | 0-5V digital    | Square wave when active
```

### Performance Optimization

#### Reducing False Triggers

1. **PIR Sensor Adjustment**
   ```cpp
   // Increase debounce time
   #define DEBOUNCE_TIME 1000  // 1 second
   
   // Add motion confirmation
   bool confirmMotion(int pirPin) {
     if (digitalRead(pirPin)) {
       delay(100);
       return digitalRead(pirPin);  // Confirm still active
     }
     return false;
   }
   ```

2. **Environmental Filtering**
   ```cpp
   // Add time-of-day filtering
   bool isActiveHours() {
     // Add RTC and check time
     return true;  // Simplified
   }
   ```

#### Improving Responsiveness

1. **Faster Animation**
   ```cpp
   #define ANIMATION_SPEED 25  // Faster animation
   #define FADE_STEPS 10       // Fewer steps
   ```

2. **Predictive Activation**
   ```cpp
   // Pre-load next animation based on direction
   void predictiveMode() {
     if (pirBottomState && !pirTopState) {
       // Prepare upward animation
     }
   }
   ```

## Hardware Modifications

### Signal Line Improvements

#### For Long Cable Runs (>5m)
1. **Add Signal Repeater**
   ```
   Components needed:
   - 74HC14 Schmitt trigger
   - 0.1µF capacitor
   - 10kΩ resistor
   ```

2. **Differential Signaling**
   ```
   Use RS-485 transceivers for noise immunity:
   - MAX485 at both ends
   - Twisted pair cable
   - 120Ω termination resistors
   ```

### Power System Upgrades

#### LED Strip Power Injection
For better LED performance on long strips:

```
Injection Points:
- Beginning: Full 12V power
- Middle (3.5m): Additional 12V injection
- End: Monitor voltage drop

Wire Gauge:
- Main power: 16 AWG minimum
- Injection: 18 AWG minimum
```

#### Backup Power Option
```cpp
// Add battery backup monitoring
#define BATTERY_PIN A0
#define LOW_BATTERY_THRESHOLD 3.3

void checkBattery() {
  float voltage = analogRead(BATTERY_PIN) * (5.0/1023.0);
  if (voltage < LOW_BATTERY_THRESHOLD) {
    // Reduce brightness or disable features
    FastLED.setBrightness(50);
  }
}
```

## Preventive Maintenance

### Monthly Checks
- [ ] Clean PIR sensor lenses
- [ ] Check LED strip adhesion
- [ ] Verify all connections tight
- [ ] Test motion detection range

### Quarterly Checks
- [ ] Measure supply voltages
- [ ] Check cable condition
- [ ] Update software if needed
- [ ] Calibrate PIR sensors

### Annual Checks
- [ ] Replace backup battery (if installed)
- [ ] Deep clean all components
- [ ] Check for corrosion
- [ ] Performance optimization review

## Emergency Procedures

### System Won't Start
1. Disconnect all power
2. Check fuses/breakers
3. Verify power supply output
4. Reconnect components one by one

### Overheating
1. Immediately disconnect power
2. Check for short circuits
3. Verify adequate ventilation
4. Inspect for damaged components

### Electrical Safety
- Always disconnect power before working
- Use proper PPE (safety glasses, etc.)
- Have fire extinguisher nearby
- Know location of main breaker

## Getting Help

### Online Resources
- Arduino Forum: https://forum.arduino.cc/
- FastLED Community: https://github.com/FastLED/FastLED
- Reddit: r/arduino, r/FastLED

### Professional Support
- Local electrician for power issues
- Electronics repair shop for component problems
- Arduino consultants for complex modifications

### Documentation
- Keep detailed notes of any changes
- Photo document your installation
- Save all configuration settings
- Maintain component purchase records
