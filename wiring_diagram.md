# Automatic Staircase System - <PERSON><PERSON> LED Strip Wiring Diagram

## Components Required:
- 1x Arduino Nano
- 2x PIR Motion Sensors (HC-SR501)
- 1x ARGB LED Strip (WS2812B/WS2815/SK6812 - 5V addressable)
- 1x 5V Power Supply (5-10A depending on LED count)
- Jumper wires (22-18 AWG for data, 16-14 AWG for power)
- Breadboard or PCB for connections
- 1x 330Ω resistor (for data line protection)
- 1x 1000µF capacitor (for power smoothing)
- Fuse holder and 10A fuse (safety)

## Pin Connections:

### Arduino Nano Connections:
```
Arduino Nano    |    Component
----------------|------------------
D2              |    PIR Bottom Sensor (OUT)
D3              |    PIR Top Sensor (OUT)
D6              |    LED Strip Data Pin
5V              |    PIR Sensors VCC
GND             |    PIR Sensors GND & LED Strip GND
VIN             |    External 5V Power Supply (+)
GND             |    External 5V Power Supply (-)
```

### PIR Sensor Connections:
```
PIR Sensor Pin  |    Arduino Nano
----------------|------------------
VCC             |    5V
GND             |    GND
OUT             |    D2 (Bottom) / D3 (Top)
```

### ARGB LED Strip Connections:
```
LED Strip Pin   |    Connection
----------------|------------------
5V/VCC          |    External 5V Power Supply (+) via fuse
GND             |    Arduino GND & Power Supply (-)
DIN/Data        |    Arduino D6 via 330Ω resistor
DOUT            |    Connect to next strip section (if applicable)
```

## Power Considerations for ARGB:
- Each ARGB LED (WS2812B) draws up to 60mA at full white brightness
- For 120 LEDs (8 steps × 15 LEDs): 120 × 60mA = 7.2A maximum
- **Recommended**: 5V 10A power supply for safety margin
- **Power injection**: For strips longer than 3 meters, inject power every 2-3 meters
- The Arduino sketch limits power to 4W for safety (can be adjusted)
- Connect 1000µF capacitor between power supply + and - near Arduino

## Physical Installation:
1. Mount PIR sensors at bottom and top of staircase
2. Install LED strips under each step
3. Place Arduino Nano in a protective enclosure
4. Use appropriate wire gauge for power connections
5. Ensure PIR sensors have clear view of stair area

## Configuration Notes:
- Adjust `NUM_STEPS` in code based on your staircase
- Adjust `LEDS_PER_STEP` based on your LED strip density
- Modify `STEP_DELAY` for faster/slower lighting sequence
- Adjust `TIMEOUT_DURATION` for different timeout periods
- Fine-tune PIR sensor sensitivity using onboard potentiometers

## Safety Notes:
- Use proper electrical enclosures for outdoor installations
- Ensure all connections are secure and insulated
- Test system thoroughly before final installation
- Consider adding fuses for overcurrent protection
