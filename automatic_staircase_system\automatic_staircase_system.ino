#include <Adafruit_NeoPixel.h>

#define LED_PIN      9
#define LED_COUNT    166

#define PIR_TOP_PIN     2
#define PIR_BOTTOM_PIN  3

Adafruit_NeoPixel strip(LED_COUNT, LED_PIN, NEO_GRB + NEO_KHZ800);

// Motion control variables
unsigned long lastMotionTime = 0;
unsigned long motionTimeout = 180000; // 3 minutes

bool motionInProgress = false;
String motionDirection = "";
int motionSpeed = 20;  // Delay in ms between LEDs (lower = faster)
unsigned long cooldownDelay = 2000;  // Delay after motion completes (in ms)

void setup() {
  strip.begin();
  strip.show(); // Initialize all LEDs off

  pinMode(PIR_TOP_PIN, INPUT);
  pinMode(PIR_BOTTOM_PIN, INPUT);

  Serial.begin(9600);
}

void loop() {
  bool topSensor = digitalRead(PIR_TOP_PIN);
  bool bottomSensor = digitalRead(PIR_BOTTOM_PIN);
  unsigned long currentTime = millis();

  if (!motionInProgress) {
    if (topSensor == HIGH) {
      Serial.println("Motion detected at TOP (Entry)");
      motionInProgress = true;
      motionDirection = "TOP_TO_BOTTOM";
      animateOnTopToBottom();
      lastMotionTime = currentTime;
    } else if (bottomSensor == HIGH) {
      Serial.println("Motion detected at BOTTOM (Entry)");
      motionInProgress = true;
      motionDirection = "BOTTOM_TO_TOP";
      animateOnBottomToTop();
      lastMotionTime = currentTime;
    }
  } else {
    // While motion is active, look only for EXIT
    if (motionDirection == "TOP_TO_BOTTOM" && bottomSensor == HIGH) {
      Serial.println("Exit at BOTTOM");
      animateOffTopToBottom();
      motionInProgress = false;
      delay(cooldownDelay);  // Prevent immediate retrigger
    } else if (motionDirection == "BOTTOM_TO_TOP" && topSensor == HIGH) {
      Serial.println("Exit at TOP");
      animateOffBottomToTop();
      motionInProgress = false;
      delay(cooldownDelay);  // Prevent immediate retrigger
    } else if (currentTime - lastMotionTime > motionTimeout) {
      Serial.println("Idle timeout. Fading red...");
      if (motionDirection == "TOP_TO_BOTTOM") {
        animateRedFadeTopToBottom();
      } else if (motionDirection == "BOTTOM_TO_TOP") {
        animateRedFadeBottomToTop();
      }
      motionInProgress = false;
      delay(cooldownDelay);  // Prevent immediate retrigger
    }
  }
}

// ========== LED Animations ==========
void animateOnTopToBottom() {
  for (int i = 0; i < LED_COUNT; i++) {
    strip.setPixelColor(i, iceBlue());
    strip.show();
    delay(motionSpeed);
  }
}

void animateOnBottomToTop() {
  for (int i = LED_COUNT - 1; i >= 0; i--) {
    strip.setPixelColor(i, iceBlue());
    strip.show();
    delay(motionSpeed);
  }
}

void animateOffTopToBottom() {
  for (int i = 0; i < LED_COUNT; i++) {
    strip.setPixelColor(i, 0);
    strip.show();
    delay(motionSpeed);
  }
}

void animateOffBottomToTop() {
  for (int i = LED_COUNT - 1; i >= 0; i--) {
    strip.setPixelColor(i, 0);
    strip.show();
    delay(motionSpeed);
  }
}

void animateRedFadeTopToBottom() {
  for (int i = 0; i < LED_COUNT; i++) {
    strip.setPixelColor(i, redColor());
    strip.show();
    delay(motionSpeed);
  }
  delay(800);
  animateOffTopToBottom();
}

void animateRedFadeBottomToTop() {
  for (int i = LED_COUNT - 1; i >= 0; i--) {
    strip.setPixelColor(i, redColor());
    strip.show();
    delay(motionSpeed);
  }
  delay(800);
  animateOffBottomToTop();
}

// ========== Colors ==========
uint32_t iceBlue() {
  return strip.Color(0, 255, 255);  // Cyan-like ice blue
}

uint32_t redColor() {
  return strip.Color(255, 0, 0);
}
