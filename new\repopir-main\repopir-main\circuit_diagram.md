# Staircase Automation Circuit Diagram

## Component List

### Main Components
- **Arduino Nano** (1x)
- **PIR Motion Sensors HC-SR501** (2x)
- **12V ARGB LED Strip** (7 meters, WS2812B compatible)
- **Buck Converter** (12V to 5V, 3A minimum)
- **Logic Level Shifter** (3.3V/5V to 5V)
- **Capacitors** (1000µF, 16V for power smoothing)
- **Resistors** (470Ω for LED data line)

### Power Supply
- **12V Power Supply** (minimum 5A for 7m LED strip)
- **5V Regulator** (for Arduino, can use USB power or separate 5V supply)

## Wiring Connections

### Arduino Nano Connections
```
Arduino Pin  | Connection
-------------|------------------
D2           | PIR Bottom Signal
D3           | PIR Top Signal  
D6           | LED Data (through level shifter)
D7           | LED Power Control (optional)
5V           | PIR Bottom VCC
GND          | Common Ground
VIN          | 12V (if powering Arduino from 12V)
```

### PIR Sensor 1 (Bottom - Near Arduino)
```
PIR Pin      | Connection
-------------|------------------
VCC          | Arduino 5V
GND          | Arduino GND
OUT          | Arduino D2
```

### PIR Sensor 2 (Top - 7m away)
```
PIR Pin      | Connection
-------------|------------------
VCC          | Buck Converter 5V Output
GND          | Buck Converter GND
OUT          | Long signal cable to Arduino D3
```

### LED Strip Connections
```
LED Strip    | Connection
-------------|------------------
+12V         | 12V Power Supply +
GND          | Common Ground
DATA         | Level Shifter Output
```

### Buck Converter
```
Input        | Output
-------------|------------------
12V+         | 5V+ (to PIR Top)
12V-         | 5V- (to PIR Top)
```

## Circuit Diagram (ASCII)

```
                    12V Power Supply (5A+)
                         |
                    +----+----+
                    |         |
                    |    Buck Converter
                    |    (12V → 5V)
                    |         |
                    |    +----+----+
                    |    |         |
                    |    |    PIR Sensor 2 (Top)
                    |    |    [7m signal cable]
                    |    |         |
    Arduino Nano    |    |         |
    +-----------+   |    |         |
    |D2      5V |---+    |         |
    |D3      GND|--------+---------+
    |D6      VIN|---+              |
    |D7         |   |              |
    |       GND |---+              |
    +-----------+   |              |
         |          |              |
    PIR Sensor 1    |              |
    (Bottom)        |              |
         |          |              |
    Level Shifter   |              |
         |          |              |
    LED Strip (7m)  |              |
    [WS2812B]       |              |
         |          |              |
         +----------+              |
                    |              |
                Common Ground -----+
```

## Signal Cable Considerations (7m length)

### For PIR Sensor Signal (7m)
- Use **shielded cable** (Cat5e/Cat6 recommended)
- Add **pull-up resistor** (10kΩ) at Arduino end
- Consider **signal conditioning** if noise issues occur

### Cable Specifications
```
Wire Type    | Purpose           | Specification
-------------|-------------------|------------------
Power (+5V)  | PIR Top Power     | 18 AWG minimum
Ground       | PIR Top Ground    | 18 AWG minimum  
Signal       | PIR Top Output    | 22-24 AWG shielded
```

## Power Calculations

### LED Strip Power (7m)
- **Typical consumption**: 60 LEDs/meter × 7m = 420 LEDs
- **Power per LED**: ~0.3W at full brightness
- **Total power**: 420 × 0.3W = 126W
- **Current at 12V**: 126W ÷ 12V = 10.5A
- **Recommended supply**: 12V 12A minimum

### Arduino + PIR Power
- **Arduino Nano**: ~20mA
- **PIR Sensors**: ~65mA each
- **Total**: ~150mA at 5V

## Safety Considerations

1. **Fusing**: Add 15A fuse on 12V supply
2. **Heat dissipation**: Ensure buck converter has adequate cooling
3. **Wire gauge**: Use appropriate wire gauge for current capacity
4. **Grounding**: Ensure all grounds are properly connected
5. **Isolation**: Consider optoisolators for long signal runs if EMI issues occur

## Optional Enhancements

1. **Capacitive smoothing**: Add 1000µF capacitor near LED strip
2. **EMI filtering**: Add ferrite cores on long cables
3. **Voltage monitoring**: Monitor supply voltage for brownout detection
4. **Temperature sensing**: Add temperature sensor for thermal protection
